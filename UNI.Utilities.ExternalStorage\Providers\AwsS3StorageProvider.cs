using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Util;
using Microsoft.Extensions.Logging;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Exceptions;
using UNI.Utilities.ExternalStorage.Models;
using S3BucketInfo = Amazon.S3.Model.BucketInfo;

namespace UNI.Utilities.ExternalStorage.Providers
{
    /// <summary>
    /// AWS S3 storage provider implementation
    /// </summary>
    public class AwsS3StorageProvider : BaseStorageProvider<AwsS3StorageSettings>
    {
        private readonly IAmazonS3 _s3Client;

        /// <summary>
        /// Constructor for AwsS3StorageProvider
        /// </summary>
        /// <param name="settings">AWS S3 storage settings</param>
        /// <param name="logger">Logger instance</param>
        public AwsS3StorageProvider(AwsS3StorageSettings settings, ILogger<AwsS3StorageProvider> logger)
            : base(settings, logger)
        {
            _s3Client = CreateS3Client();
        }

        /// <summary>
        /// Storage provider type
        /// </summary>
        public override StorageProviderType ProviderType => StorageProviderType.AwsS3;

        /// <summary>
        /// Storage provider name
        /// </summary>
        public override string ProviderName => "AWS S3";

        /// <summary>
        /// Validate AWS S3-specific settings
        /// </summary>
        protected override void ValidateSettings()
        {
            base.ValidateSettings();

            if (string.IsNullOrWhiteSpace(_settings.AccessKeyId))
                throw new StorageConfigurationException("AWS AccessKeyId is required", ProviderType);

            if (string.IsNullOrWhiteSpace(_settings.SecretAccessKey))
                throw new StorageConfigurationException("AWS SecretAccessKey is required", ProviderType);

            if (string.IsNullOrWhiteSpace(_settings.Region))
                throw new StorageConfigurationException("AWS Region is required", ProviderType);
        }

        /// <summary>
        /// Create AWS S3 client
        /// </summary>
        /// <returns>AWS S3 client</returns>
        private IAmazonS3 CreateS3Client()
        {
            var config = new AmazonS3Config
            {
                RegionEndpoint = RegionEndpoint.GetBySystemName(_settings.Region),
                Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds),
                ForcePathStyle = _settings.ForcePathStyle,
                UseAccelerateEndpoint = _settings.UseAccelerateEndpoint,
                UseDualstackEndpoint = _settings.UseDualstackEndpoint
            };

            if (!string.IsNullOrWhiteSpace(_settings.ServiceUrl))
            {
                config.ServiceURL = _settings.ServiceUrl;
                config.ForcePathStyle = true; // Required for custom endpoints
            }

            if (!string.IsNullOrWhiteSpace(_settings.SessionToken))
            {
                return new AmazonS3Client(_settings.AccessKeyId, _settings.SecretAccessKey, _settings.SessionToken, config);
            }

            return new AmazonS3Client(_settings.AccessKeyId, _settings.SecretAccessKey, config);
        }

        #region Bucket Operations

        public override async Task<bool> BucketExistsAsync(string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var response = await AmazonS3Util.DoesS3BucketExistV2Async(_s3Client, resolvedBucketName);
                return response;
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                _logger.LogError(ex, "Failed to check if bucket {BucketName} exists", resolvedBucketName);
                throw new StorageException($"Failed to check if bucket '{resolvedBucketName}' exists", ProviderType, innerException: ex);
            }
        }

        public override async Task CreateBucketAsync(string? bucketName = null, string? region = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var request = new PutBucketRequest
                {
                    BucketName = resolvedBucketName,
                    UseClientRegion = true
                };

                if (!string.IsNullOrWhiteSpace(region))
                {
                    request.BucketRegion = S3Region.FindValue(region);
                }

                await _s3Client.PutBucketAsync(request, cancellationToken);
                _logger.LogInformation("Created bucket {BucketName}", bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create bucket {BucketName}", bucketName);
                throw new StorageException($"Failed to create bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DeleteBucketAsync(string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await _s3Client.DeleteBucketAsync(resolvedBucketName, cancellationToken);
                _logger.LogInformation("Deleted bucket {BucketName}", resolvedBucketName);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                _logger.LogError(ex, "Failed to delete bucket {BucketName}", resolvedBucketName);
                throw new StorageException($"Failed to delete bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<UNI.Utilities.ExternalStorage.Models.BucketInfo>> ListBucketsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var response = await _s3Client.ListBucketsAsync(cancellationToken);
                return response.Buckets.Select(ConvertToBucketInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to list buckets");
                throw new StorageException("Failed to list buckets", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region Object Operations

        public override async Task<StorageUploadResult> UploadObjectAsync(
            string objectName,
            Stream stream,
            string? bucketName = null,
            long? size = null,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await EnsureBucketExistsAsync(resolvedBucketName, cancellationToken);

                var request = new PutObjectRequest
                {
                    BucketName = resolvedBucketName,
                    Key = objectName,
                    InputStream = stream,
                    ContentType = contentType ?? GetContentType(objectName),
                    AutoCloseStream = false
                };

                if (!string.IsNullOrWhiteSpace(_settings.StorageClass))
                {
                    request.StorageClass = S3StorageClass.FindValue(_settings.StorageClass);
                }

                if (!string.IsNullOrWhiteSpace(_settings.ServerSideEncryption))
                {
                    request.ServerSideEncryptionMethod = ServerSideEncryptionMethod.FindValue(_settings.ServerSideEncryption);
                    
                    if (!string.IsNullOrWhiteSpace(_settings.KmsKeyId))
                    {
                        request.ServerSideEncryptionKeyManagementServiceKeyId = _settings.KmsKeyId;
                    }
                }

                if (metadata != null)
                {
                    foreach (var kvp in metadata)
                    {
                        request.Metadata.Add(kvp.Key, kvp.Value);
                    }
                }

                var response = await _s3Client.PutObjectAsync(request, cancellationToken);

                return new StorageUploadResult
                {
                    BucketName = bucketName,
                    ObjectName = objectName,
                    Size = stream.Length,
                    ETag = response.ETag?.Trim('"'),
                    ContentType = request.ContentType,
                    UploadedAt = DateTime.UtcNow,
                    Metadata = metadata ?? new Dictionary<string, string>(),
                    ProviderData = new Dictionary<string, object>
                    {
                        ["S3VersionId"] = response.VersionId ?? string.Empty,
                        ["S3RequestId"] = response.ResponseMetadata?.RequestId ?? string.Empty
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload object {ObjectName} to bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to upload object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageUploadResult> UploadFileAsync(
            string objectName,
            string filePath,
            string? bucketName = null,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File not found: {filePath}");
                }

                using var fileStream = File.OpenRead(filePath);
                return await UploadObjectAsync(objectName, fileStream, bucketName, fileStream.Length, contentType, metadata, cancellationToken);
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to upload file {FilePath} as object {ObjectName} to bucket {BucketName}", filePath, objectName, bucketName);
                throw new StorageException($"Failed to upload file '{filePath}' as object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageUploadResult> UploadLargeFileAsync(
            string objectName,
            string filePath,
            string? bucketName = null,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            long partSize = 67108864,
            IProgress<StorageUploadProgress>? progressCallback = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File not found: {filePath}");
                }

                await EnsureBucketExistsAsync(bucketName, cancellationToken);

                var fileInfo = new FileInfo(filePath);
                var fileSize = fileInfo.Length;

                // Use multipart upload for large files
                if (fileSize > partSize)
                {
                    return await UploadLargeFileMultipartAsync(objectName, filePath, bucketName, contentType, metadata, partSize, progressCallback, cancellationToken);
                }
                else
                {
                    // Use regular upload for smaller files
                    return await UploadFileAsync(objectName, filePath, bucketName, contentType, metadata, cancellationToken);
                }
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to upload large file {FilePath} as object {ObjectName} to bucket {BucketName}", filePath, objectName, bucketName);
                throw new StorageException($"Failed to upload large file '{filePath}' as object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DownloadObjectAsync(string objectName, Stream stream, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var request = new GetObjectRequest
                {
                    BucketName = resolvedBucketName,
                    Key = objectName
                };

                using var response = await _s3Client.GetObjectAsync(request, cancellationToken);
                await response.ResponseStream.CopyToAsync(stream, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to download object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to download object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DownloadFileAsync(string objectName, string filePath, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using var fileStream = File.Create(filePath);
                await DownloadObjectAsync(objectName, fileStream, bucketName, cancellationToken);
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to download object {ObjectName} from bucket {BucketName} to file {FilePath}", objectName, bucketName, filePath);
                throw new StorageException($"Failed to download object '{objectName}' from bucket '{bucketName}' to file '{filePath}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<byte[]> GetObjectBytesAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                await DownloadObjectAsync(objectName, memoryStream, bucketName, cancellationToken);
                return memoryStream.ToArray();
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to get object bytes for {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get object bytes for '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<bool> ObjectExistsAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var request = new GetObjectMetadataRequest
                {
                    BucketName = resolvedBucketName,
                    Key = objectName
                };

                await _s3Client.GetObjectMetadataAsync(request, cancellationToken);
                return true;
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check if object {ObjectName} exists in bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to check if object '{objectName}' exists in bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DeleteObjectAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var request = new DeleteObjectRequest
                {
                    BucketName = resolvedBucketName,
                    Key = objectName
                };

                await _s3Client.DeleteObjectAsync(request, cancellationToken);
                _logger.LogDebug("Deleted object {ObjectName} from bucket {BucketName}", objectName, bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to delete object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<StorageDeleteResult>> DeleteObjectsAsync(IEnumerable<string> objectNames, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var objectNamesList = objectNames.ToList();
                if (!objectNamesList.Any())
                {
                    return Enumerable.Empty<StorageDeleteResult>();
                }

                var resolvedBucketName = GetBucketName(bucketName);
                var request = new DeleteObjectsRequest
                {
                    BucketName = resolvedBucketName
                };

                foreach (var objectName in objectNamesList)
                {
                    request.Objects.Add(new KeyVersion { Key = objectName });
                }

                var response = await _s3Client.DeleteObjectsAsync(request, cancellationToken);

                var results = new List<StorageDeleteResult>();

                // Add successful deletions
                foreach (var deleted in response.DeletedObjects)
                {
                    results.Add(new StorageDeleteResult
                    {
                        ObjectName = deleted.Key,
                        Success = true
                    });
                }

                // Add failed deletions
                foreach (var error in response.DeleteErrors)
                {
                    results.Add(new StorageDeleteResult
                    {
                        ObjectName = error.Key,
                        Success = false,
                        ErrorMessage = error.Message,
                        ErrorCode = error.Code
                    });
                }

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete objects from bucket {BucketName}", bucketName);
                throw new StorageException($"Failed to delete objects from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageObjectInfo> GetObjectInfoAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var request = new GetObjectMetadataRequest
                {
                    BucketName = resolvedBucketName,
                    Key = objectName
                };

                var response = await _s3Client.GetObjectMetadataAsync(request, cancellationToken);
                return ConvertToStorageObjectInfo(objectName, response);
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new ObjectNotFoundException(resolvedBucketName, objectName, ProviderType, ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get object info for {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get object info for '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<StorageObjectInfo>> ListObjectsAsync(string? prefix = null, bool recursive = true, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var request = new ListObjectsV2Request
                {
                    BucketName = resolvedBucketName,
                    Prefix = prefix,
                    Delimiter = recursive ? null : "/"
                };

                var objects = new List<StorageObjectInfo>();
                ListObjectsV2Response response;

                do
                {
                    response = await _s3Client.ListObjectsV2Async(request, cancellationToken);
                    
                    foreach (var obj in response.S3Objects)
                    {
                        objects.Add(ConvertToStorageObjectInfo(obj));
                    }

                    // Add common prefixes as directories if not recursive
                    if (!recursive)
                    {
                        foreach (var commonPrefix in response.CommonPrefixes)
                        {
                            objects.Add(new StorageObjectInfo
                            {
                                ObjectName = commonPrefix,
                                Size = 0,
                                LastModified = DateTime.MinValue,
                                IsDirectory = true,
                                ContentType = "application/x-directory"
                            });
                        }
                    }

                    request.ContinuationToken = response.NextContinuationToken;
                } while (response.IsTruncated);

                return objects;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to list objects in bucket {BucketName}", bucketName);
                throw new StorageException($"Failed to list objects in bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region URL Operations

        public override async Task<string> GetPresignedDownloadUrlAsync(string objectName, TimeSpan expiryTimeSpan, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = resolvedBucketName,
                    Key = objectName,
                    Verb = HttpVerb.GET,
                    Expires = DateTime.UtcNow.Add(expiryTimeSpan)
                };

                return await Task.FromResult(_s3Client.GetPreSignedURL(request));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get presigned download URL for object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get presigned download URL for object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<string> GetPresignedUploadUrlAsync(string objectName, TimeSpan expiryTimeSpan, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = resolvedBucketName,
                    Key = objectName,
                    Verb = HttpVerb.PUT,
                    Expires = DateTime.UtcNow.Add(expiryTimeSpan)
                };

                return await Task.FromResult(_s3Client.GetPreSignedURL(request));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get presigned upload URL for object {ObjectName} to bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get presigned upload URL for object '{objectName}' to bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<string> GetPreviewUrlAsync(string bucketName, string objectName, CancellationToken cancellationToken = default)
        {
            try
            {
                // For S3, preview URLs are typically presigned URLs with longer expiry
                var expiryTimeSpan = TimeSpan.FromHours(24); // 24 hours default
                return await GetPresignedDownloadUrlAsync(objectName, expiryTimeSpan, bucketName, cancellationToken);
            }
            catch (Exception ex) when (!(ex is StorageException))
            {
                _logger.LogError(ex, "Failed to get preview URL for object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw new StorageException($"Failed to get preview URL for object '{objectName}' from bucket '{bucketName}'", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region Helper Methods

        private async Task<StorageUploadResult> UploadLargeFileMultipartAsync(
            string objectName,
            string filePath,
            string? bucketName,
            string? contentType,
            Dictionary<string, string>? metadata,
            long partSize, 
            IProgress<StorageUploadProgress>? progressCallback, 
            CancellationToken cancellationToken)
        {
            var fileInfo = new FileInfo(filePath);
            var fileSize = fileInfo.Length;
            var totalParts = (int)Math.Ceiling((double)fileSize / partSize);

            // Initiate multipart upload
            var resolvedBucketName = GetBucketName(bucketName);
            var initiateRequest = new InitiateMultipartUploadRequest
            {
                BucketName = resolvedBucketName,
                Key = objectName,
                ContentType = contentType ?? GetContentType(objectName)
            };

            if (!string.IsNullOrWhiteSpace(_settings.StorageClass))
            {
                initiateRequest.StorageClass = S3StorageClass.FindValue(_settings.StorageClass);
            }

            if (metadata != null)
            {
                foreach (var kvp in metadata)
                {
                    initiateRequest.Metadata.Add(kvp.Key, kvp.Value);
                }
            }

            var initiateResponse = await _s3Client.InitiateMultipartUploadAsync(initiateRequest, cancellationToken);
            var uploadId = initiateResponse.UploadId;

            var uploadedParts = new List<PartETag>();
            long uploadedBytes = 0;

            try
            {
                using var fileStream = File.OpenRead(filePath);

                for (int partNumber = 1; partNumber <= totalParts; partNumber++)
                {
                    var currentPartSize = Math.Min(partSize, fileSize - uploadedBytes);
                    var buffer = new byte[currentPartSize];
                    await fileStream.ReadAsync(buffer, 0, (int)currentPartSize, cancellationToken);

                    using var partStream = new MemoryStream(buffer);
                    var uploadPartRequest = new UploadPartRequest
                    {
                        BucketName = resolvedBucketName,
                        Key = objectName,
                        UploadId = uploadId,
                        PartNumber = partNumber,
                        InputStream = partStream,
                        PartSize = currentPartSize
                    };

                    var uploadPartResponse = await _s3Client.UploadPartAsync(uploadPartRequest, cancellationToken);
                    uploadedParts.Add(new PartETag(partNumber, uploadPartResponse.ETag));

                    uploadedBytes += currentPartSize;

                    // Report progress
                    progressCallback?.Report(new StorageUploadProgress
                    {
                        TotalBytes = fileSize,
                        UploadedBytes = uploadedBytes,
                        CurrentPart = partNumber,
                        TotalParts = totalParts
                    });
                }

                // Complete multipart upload
                var completeRequest = new CompleteMultipartUploadRequest
                {
                    BucketName = resolvedBucketName,
                    Key = objectName,
                    UploadId = uploadId,
                    PartETags = uploadedParts
                };

                var completeResponse = await _s3Client.CompleteMultipartUploadAsync(completeRequest, cancellationToken);

                return new StorageUploadResult
                {
                    BucketName = resolvedBucketName,
                    ObjectName = objectName,
                    Size = fileSize,
                    ETag = completeResponse.ETag?.Trim('"'),
                    ContentType = contentType ?? GetContentType(objectName),
                    UploadedAt = DateTime.UtcNow,
                    Metadata = metadata ?? new Dictionary<string, string>(),
                    ProviderData = new Dictionary<string, object>
                    {
                        ["S3VersionId"] = completeResponse.VersionId ?? string.Empty,
                        ["S3Location"] = completeResponse.Location ?? string.Empty,
                        ["S3UploadId"] = uploadId
                    }
                };
            }
            catch
            {
                // Abort multipart upload on error
                try
                {
                    await _s3Client.AbortMultipartUploadAsync(new AbortMultipartUploadRequest
                    {
                        BucketName = bucketName,
                        Key = objectName,
                        UploadId = uploadId
                    }, cancellationToken);
                }
                catch (Exception abortEx)
                {
                    _logger.LogWarning(abortEx, "Failed to abort multipart upload {UploadId} for object {ObjectName}", uploadId, objectName);
                }

                throw;
            }
        }

        private UNI.Utilities.ExternalStorage.Models.BucketInfo ConvertToBucketInfo(S3Bucket s3Bucket)
        {
            return new UNI.Utilities.ExternalStorage.Models.BucketInfo
            {
                Name = s3Bucket.BucketName,
                CreationDate = s3Bucket.CreationDate,
                Metadata = new Dictionary<string, string>()
            };
        }

        private StorageObjectInfo ConvertToStorageObjectInfo(S3Object s3Object)
        {
            return new StorageObjectInfo
            {
                ObjectName = s3Object.Key,
                Size = s3Object.Size,
                LastModified = s3Object.LastModified,
                ETag = s3Object.ETag?.Trim('"'),
                StorageClass = s3Object.StorageClass?.Value,
                IsDirectory = s3Object.Key.EndsWith("/") && s3Object.Size == 0,
                Metadata = new Dictionary<string, string>(),
                ProviderData = new Dictionary<string, object>
                {
                    ["S3Owner"] = s3Object.Owner?.DisplayName ?? string.Empty,
                    ["S3OwnerId"] = s3Object.Owner?.Id ?? string.Empty
                }
            };
        }

        private StorageObjectInfo ConvertToStorageObjectInfo(string objectName, GetObjectMetadataResponse metadata)
        {
            return new StorageObjectInfo
            {
                ObjectName = objectName,
                Size = metadata.ContentLength,
                LastModified = metadata.LastModified,
                ETag = metadata.ETag?.Trim('"'),
                ContentType = metadata.Headers.ContentType,
                StorageClass = metadata.StorageClass?.Value,
                IsDirectory = false,
                Metadata = new Dictionary<string, string>(),
                ProviderData = new Dictionary<string, object>
                {
                    ["S3VersionId"] = metadata.VersionId ?? string.Empty,
                    ["S3ServerSideEncryption"] = metadata.ServerSideEncryptionMethod?.Value ?? string.Empty
                }
            };
        }

        #endregion

        #region Dispose

        protected override void DisposeCore()
        {
            _s3Client?.Dispose();
        }

        #endregion
    }
}
