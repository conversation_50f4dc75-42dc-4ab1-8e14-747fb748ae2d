using UNI.Utilities.ExternalStorage.Models;

namespace UNI.Utilities.ExternalStorage.Abstractions
{
    /// <summary>
    /// Interface for external storage operations that can be implemented by different storage providers
    /// </summary>
    public interface IStorageService : IDisposable
    {
        #region Bucket/Container Operations
        
        /// <summary>
        /// Check if a bucket/container exists
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if bucket exists, false otherwise</returns>
        Task<bool> BucketExistsAsync(string bucketName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a new bucket/container
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="region">Region for the bucket (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task CreateBucketAsync(string bucketName, string? region = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a bucket/container
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DeleteBucketAsync(string bucketName, CancellationToken cancellationToken = default);

        /// <summary>
        /// List all buckets/containers
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of bucket information</returns>
        Task<IEnumerable<UNI.Utilities.ExternalStorage.Models.BucketInfo>> ListBucketsAsync(CancellationToken cancellationToken = default);

        #endregion

        #region Object Operations

        /// <summary>
        /// Upload an object from a stream
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="stream">Stream containing the object data</param>
        /// <param name="size">Size of the object (optional)</param>
        /// <param name="contentType">Content type of the object (optional)</param>
        /// <param name="metadata">Additional metadata (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Upload result with object information</returns>
        Task<StorageUploadResult> UploadObjectAsync(
            string bucketName,
            string objectName,
            Stream stream,
            long? size = null,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Upload a file
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="filePath">Path to the file to upload</param>
        /// <param name="contentType">Content type of the object (optional)</param>
        /// <param name="metadata">Additional metadata (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Upload result with object information</returns>
        Task<StorageUploadResult> UploadFileAsync(
            string bucketName,
            string objectName,
            string filePath,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Upload a large file with progress tracking
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="filePath">Path to the file to upload</param>
        /// <param name="contentType">Content type of the object (optional)</param>
        /// <param name="metadata">Additional metadata (optional)</param>
        /// <param name="partSize">Size of each part for multipart upload</param>
        /// <param name="progressCallback">Progress callback (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Upload result with object information</returns>
        Task<StorageUploadResult> UploadLargeFileAsync(
            string bucketName,
            string objectName,
            string filePath,
            string? contentType = null,
            Dictionary<string, string>? metadata = null,
            long partSize = 64 * 1024 * 1024,
            IProgress<StorageUploadProgress>? progressCallback = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Download an object to a stream
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="stream">Stream to write the object data to</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DownloadObjectAsync(string bucketName, string objectName, Stream stream, CancellationToken cancellationToken = default);

        /// <summary>
        /// Download an object to a file
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="filePath">Path where to save the downloaded file</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DownloadFileAsync(string bucketName, string objectName, string filePath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get object data as byte array
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Object data as byte array</returns>
        Task<byte[]> GetObjectBytesAsync(string bucketName, string objectName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Check if an object exists
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if object exists, false otherwise</returns>
        Task<bool> ObjectExistsAsync(string bucketName, string objectName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete an object
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task DeleteObjectAsync(string bucketName, string objectName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete multiple objects
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectNames">Names of the objects to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of deletion results</returns>
        Task<IEnumerable<StorageDeleteResult>> DeleteObjectsAsync(string bucketName, IEnumerable<string> objectNames, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get object information
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Object information</returns>
        Task<StorageObjectInfo> GetObjectInfoAsync(string bucketName, string objectName, CancellationToken cancellationToken = default);

        /// <summary>
        /// List objects in a bucket/container
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="prefix">Prefix to filter objects (optional)</param>
        /// <param name="recursive">Whether to list objects recursively (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of object information</returns>
        Task<IEnumerable<StorageObjectInfo>> ListObjectsAsync(string bucketName, string? prefix = null, bool recursive = true, CancellationToken cancellationToken = default);

        #endregion

        #region URL Operations

        /// <summary>
        /// Generate a presigned URL for downloading an object
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="expiryTimeSpan">Expiry time for the URL</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Presigned download URL</returns>
        Task<string> GetPresignedDownloadUrlAsync(string bucketName, string objectName, TimeSpan expiryTimeSpan, CancellationToken cancellationToken = default);

        /// <summary>
        /// Generate a presigned URL for uploading an object
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="expiryTimeSpan">Expiry time for the URL</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Presigned upload URL</returns>
        Task<string> GetPresignedUploadUrlAsync(string bucketName, string objectName, TimeSpan expiryTimeSpan, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a preview URL for an object (public access URL)
        /// </summary>
        /// <param name="bucketName">Name of the bucket/container</param>
        /// <param name="objectName">Name of the object</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Preview URL</returns>
        Task<string> GetPreviewUrlAsync(string bucketName, string objectName, CancellationToken cancellationToken = default);

        #endregion

        #region Provider Information

        /// <summary>
        /// Get the storage provider type
        /// </summary>
        StorageProviderType ProviderType { get; }

        /// <summary>
        /// Get the storage provider name
        /// </summary>
        string ProviderName { get; }

        #endregion
    }
}
