using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Extensions;
using UNI.Utilities.ExternalStorage.Models;

namespace UNI.Utilities.ExternalStorage.Examples
{
    /// <summary>
    /// Basic usage example for the External Storage service
    /// </summary>
    public class BasicUsageExample
    {
        private readonly IStorageService _storageService;
        private readonly ILogger<BasicUsageExample> _logger;

        public BasicUsageExample(IStorageService storageService, ILogger<BasicUsageExample> logger)
        {
            _storageService = storageService;
            _logger = logger;
        }

        /// <summary>
        /// Demonstrates basic file operations
        /// </summary>
        public async Task RunBasicOperationsAsync()
        {
            var bucketName = "example-bucket";
            var objectName = "example-file.txt";
            var filePath = "example-file.txt";

            try
            {
                _logger.LogInformation("Starting basic storage operations example");

                // 1. Create bucket if it doesn't exist
                await EnsureBucketExistsAsync(bucketName);

                // 2. Upload a file
                await UploadFileExampleAsync(bucketName, objectName, filePath);

                // 3. Check if object exists
                await CheckObjectExistsAsync(bucketName, objectName);

                // 4. Get object information
                await GetObjectInfoAsync(bucketName, objectName);

                // 5. Download the file
                await DownloadFileExampleAsync(bucketName, objectName, "downloaded-file.txt");

                // 6. Upload from stream
                await UploadFromStreamExampleAsync(bucketName, "stream-file.txt");

                // 7. Download to stream
                await DownloadToStreamExampleAsync(bucketName, "stream-file.txt");

                // 8. List objects in bucket
                await ListObjectsExampleAsync(bucketName);

                // 9. Generate presigned URLs
                await GeneratePresignedUrlsAsync(bucketName, objectName);

                // 10. Delete objects
                await DeleteObjectsExampleAsync(bucketName, new[] { objectName, "stream-file.txt" });

                _logger.LogInformation("Basic storage operations example completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during basic storage operations example");
                throw;
            }
        }

        private async Task EnsureBucketExistsAsync(string bucketName)
        {
            _logger.LogInformation("Checking if bucket {BucketName} exists", bucketName);

            var bucketExists = await _storageService.BucketExistsAsync(bucketName);
            if (!bucketExists)
            {
                _logger.LogInformation("Creating bucket {BucketName}", bucketName);
                await _storageService.CreateBucketAsync(bucketName);
                _logger.LogInformation("Bucket {BucketName} created successfully", bucketName);
            }
            else
            {
                _logger.LogInformation("Bucket {BucketName} already exists", bucketName);
            }
        }

        private async Task UploadFileExampleAsync(string bucketName, string objectName, string filePath)
        {
            _logger.LogInformation("Uploading file {FilePath} as {ObjectName} to bucket {BucketName}", filePath, objectName, bucketName);

            // Create a sample file if it doesn't exist
            if (!File.Exists(filePath))
            {
                await File.WriteAllTextAsync(filePath, "Hello, World! This is a sample file for storage testing.");
                _logger.LogInformation("Created sample file {FilePath}", filePath);
            }

            var uploadResult = await _storageService.UploadFileAsync(
                bucketName, 
                objectName, 
                filePath, 
                "text/plain",
                new Dictionary<string, string>
                {
                    ["uploaded-by"] = "BasicUsageExample",
                    ["upload-time"] = DateTime.UtcNow.ToString("O")
                });

            _logger.LogInformation("File uploaded successfully. Size: {Size} bytes, ETag: {ETag}", 
                uploadResult.Size, uploadResult.ETag);
        }

        private async Task CheckObjectExistsAsync(string bucketName, string objectName)
        {
            _logger.LogInformation("Checking if object {ObjectName} exists in bucket {BucketName}", objectName, bucketName);

            var objectExists = await _storageService.ObjectExistsAsync(bucketName, objectName);
            _logger.LogInformation("Object {ObjectName} exists: {Exists}", objectName, objectExists);
        }

        private async Task GetObjectInfoAsync(string bucketName, string objectName)
        {
            _logger.LogInformation("Getting object info for {ObjectName} in bucket {BucketName}", objectName, bucketName);

            var objectInfo = await _storageService.GetObjectInfoAsync(bucketName, objectName);
            _logger.LogInformation("Object info - Name: {Name}, Size: {Size} bytes, Last Modified: {LastModified}, Content Type: {ContentType}",
                objectInfo.ObjectName, objectInfo.Size, objectInfo.LastModified, objectInfo.ContentType);

            if (objectInfo.Metadata.Any())
            {
                _logger.LogInformation("Object metadata:");
                foreach (var metadata in objectInfo.Metadata)
                {
                    _logger.LogInformation("  {Key}: {Value}", metadata.Key, metadata.Value);
                }
            }
        }

        private async Task DownloadFileExampleAsync(string bucketName, string objectName, string downloadPath)
        {
            _logger.LogInformation("Downloading object {ObjectName} from bucket {BucketName} to {DownloadPath}", 
                objectName, bucketName, downloadPath);

            await _storageService.DownloadFileAsync(bucketName, objectName, downloadPath);

            if (File.Exists(downloadPath))
            {
                var content = await File.ReadAllTextAsync(downloadPath);
                _logger.LogInformation("Downloaded file content: {Content}", content);
            }
        }

        private async Task UploadFromStreamExampleAsync(string bucketName, string objectName)
        {
            _logger.LogInformation("Uploading from stream to {ObjectName} in bucket {BucketName}", objectName, bucketName);

            var content = "This content is uploaded from a memory stream.";
            var bytes = System.Text.Encoding.UTF8.GetBytes(content);

            using var stream = new MemoryStream(bytes);
            var uploadResult = await _storageService.UploadObjectAsync(
                bucketName, 
                objectName, 
                stream, 
                bytes.Length, 
                "text/plain");

            _logger.LogInformation("Stream upload completed. Size: {Size} bytes", uploadResult.Size);
        }

        private async Task DownloadToStreamExampleAsync(string bucketName, string objectName)
        {
            _logger.LogInformation("Downloading {ObjectName} from bucket {BucketName} to stream", objectName, bucketName);

            using var stream = new MemoryStream();
            await _storageService.DownloadObjectAsync(bucketName, objectName, stream);

            var content = System.Text.Encoding.UTF8.GetString(stream.ToArray());
            _logger.LogInformation("Downloaded content from stream: {Content}", content);
        }

        private async Task ListObjectsExampleAsync(string bucketName)
        {
            _logger.LogInformation("Listing objects in bucket {BucketName}", bucketName);

            var objects = await _storageService.ListObjectsAsync(bucketName);
            var objectsList = objects.ToList();

            _logger.LogInformation("Found {Count} objects in bucket {BucketName}", objectsList.Count, bucketName);
            foreach (var obj in objectsList)
            {
                _logger.LogInformation("  - {Name} ({Size} bytes, {LastModified})", 
                    obj.ObjectName, obj.Size, obj.LastModified);
            }
        }

        private async Task GeneratePresignedUrlsAsync(string bucketName, string objectName)
        {
            _logger.LogInformation("Generating presigned URLs for {ObjectName} in bucket {BucketName}", objectName, bucketName);

            // Generate download URL (valid for 1 hour)
            var downloadUrl = await _storageService.GetPresignedDownloadUrlAsync(bucketName, objectName, TimeSpan.FromHours(1));
            _logger.LogInformation("Presigned download URL: {Url}", downloadUrl);

            // Generate upload URL (valid for 30 minutes)
            var uploadUrl = await _storageService.GetPresignedUploadUrlAsync(bucketName, "new-upload.txt", TimeSpan.FromMinutes(30));
            _logger.LogInformation("Presigned upload URL: {Url}", uploadUrl);

            // Generate preview URL
            var previewUrl = await _storageService.GetPreviewUrlAsync(bucketName, objectName);
            _logger.LogInformation("Preview URL: {Url}", previewUrl);
        }

        private async Task DeleteObjectsExampleAsync(string bucketName, string[] objectNames)
        {
            _logger.LogInformation("Deleting {Count} objects from bucket {BucketName}", objectNames.Length, bucketName);

            var deleteResults = await _storageService.DeleteObjectsAsync(bucketName, objectNames);
            var deleteResultsList = deleteResults.ToList();

            foreach (var result in deleteResultsList)
            {
                if (result.Success)
                {
                    _logger.LogInformation("Successfully deleted object: {ObjectName}", result.ObjectName);
                }
                else
                {
                    _logger.LogWarning("Failed to delete object {ObjectName}: {ErrorMessage}", 
                        result.ObjectName, result.ErrorMessage);
                }
            }
        }

        /// <summary>
        /// Example of how to set up dependency injection for the storage service
        /// </summary>
        public static ServiceProvider CreateServiceProvider()
        {
            var services = new ServiceCollection();

            // Add logging
            services.AddLogging(builder =>
            {
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add external storage service with MinIO configuration
            services.AddExternalStorageServices(config =>
            {
                config.DefaultProvider = StorageProviderType.MinIO;
                config.MinIO = new MinIOStorageSettings
                {
                    Endpoint = "localhost:9000",
                    ProxyEndpoint = "https://files.example.com", // Optional: for public access
                    AccessKey = "minioadmin",
                    SecretKey = "minioadmin",
                    UseSSL = false,
                    CreateBucketIfNotExists = true
                };
            });

            // Add the example service
            services.AddTransient<BasicUsageExample>();

            return services.BuildServiceProvider();
        }

        /// <summary>
        /// Main entry point for running the example
        /// </summary>
        public static async Task Main(string[] args)
        {
            using var serviceProvider = CreateServiceProvider();
            var example = serviceProvider.GetRequiredService<BasicUsageExample>();

            try
            {
                await example.RunBasicOperationsAsync();
                Console.WriteLine("Example completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Example failed: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        }
    }
}
