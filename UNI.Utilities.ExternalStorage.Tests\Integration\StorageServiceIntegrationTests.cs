using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Extensions;
using UNI.Utilities.ExternalStorage.Models;
using Xunit;
using Xunit.Abstractions;

namespace UNI.Utilities.ExternalStorage.Tests.Integration
{
    /// <summary>
    /// Integration tests for storage services
    /// These tests require actual storage provider configurations to run
    /// </summary>
    public class StorageServiceIntegrationTests : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStorageService _storageService;
        private readonly string _testBucketName;
        private readonly List<string> _createdObjects;

        public StorageServiceIntegrationTests(ITestOutputHelper output)
        {
            _output = output;
            _createdObjects = new List<string>();
            _testBucketName = $"test-bucket-{Guid.NewGuid():N}";

            // Setup configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.test.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            // Setup dependency injection
            var services = new ServiceCollection();
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Debug);
            });

            services.AddExternalStorageServices(configuration.GetSection("ExternalStorage"));

            _serviceProvider = services.BuildServiceProvider();
            _storageService = _serviceProvider.GetRequiredService<IStorageService>();

            _output.WriteLine($"Using storage provider: {_storageService.ProviderName} ({_storageService.ProviderType})");
        }

        [Fact]
        public async Task BucketOperations_ShouldWorkCorrectly()
        {
            // Test bucket creation
            await _storageService.CreateBucketAsync(_testBucketName);
            _output.WriteLine($"Created bucket: {_testBucketName}");

            // Test bucket exists
            var bucketExists = await _storageService.BucketExistsAsync(_testBucketName);
            Assert.True(bucketExists);
            _output.WriteLine($"Bucket exists: {bucketExists}");

            // Test list buckets
            var buckets = await _storageService.ListBucketsAsync();
            Assert.Contains(buckets, b => b.Name == _testBucketName);
            _output.WriteLine($"Found {buckets.Count()} buckets");

            // Cleanup will be done in Dispose
        }

        [Fact]
        public async Task UploadDownloadObject_ShouldWorkCorrectly()
        {
            // Arrange
            await EnsureBucketExistsAsync();
            var objectName = $"test-object-{Guid.NewGuid():N}.txt";
            var testContent = "Hello, World! This is a test file.";
            var testBytes = System.Text.Encoding.UTF8.GetBytes(testContent);

            try
            {
                // Test upload from stream
                using var uploadStream = new MemoryStream(testBytes);
                var uploadResult = await _storageService.UploadObjectAsync(
                    objectName,
                    uploadStream,
                    _testBucketName,
                    testBytes.Length,
                    "text/plain");

                Assert.NotNull(uploadResult);
                Assert.Equal(_testBucketName, uploadResult.BucketName);
                Assert.Equal(objectName, uploadResult.ObjectName);
                Assert.Equal(testBytes.Length, uploadResult.Size);
                _output.WriteLine($"Uploaded object: {objectName}, Size: {uploadResult.Size} bytes");

                _createdObjects.Add(objectName);

                // Test object exists
                var objectExists = await _storageService.ObjectExistsAsync(objectName, _testBucketName);
                Assert.True(objectExists);
                _output.WriteLine($"Object exists: {objectExists}");

                // Test get object info
                var objectInfo = await _storageService.GetObjectInfoAsync(objectName, _testBucketName);
                Assert.NotNull(objectInfo);
                Assert.Equal(objectName, objectInfo.ObjectName);
                Assert.Equal(testBytes.Length, objectInfo.Size);
                _output.WriteLine($"Object info - Name: {objectInfo.ObjectName}, Size: {objectInfo.Size}");

                // Test download to stream
                using var downloadStream = new MemoryStream();
                await _storageService.DownloadObjectAsync(objectName, downloadStream, _testBucketName);

                var downloadedBytes = downloadStream.ToArray();
                var downloadedContent = System.Text.Encoding.UTF8.GetString(downloadedBytes);

                Assert.Equal(testContent, downloadedContent);
                _output.WriteLine($"Downloaded content: {downloadedContent}");

                // Test get object bytes
                var objectBytes = await _storageService.GetObjectBytesAsync(_testBucketName, objectName);
                var objectContent = System.Text.Encoding.UTF8.GetString(objectBytes);

                Assert.Equal(testContent, objectContent);
                _output.WriteLine($"Object bytes content: {objectContent}");
            }
            finally
            {
                // Cleanup will be done in Dispose
            }
        }

        [Fact]
        public async Task UploadDownloadFile_ShouldWorkCorrectly()
        {
            // Arrange
            await EnsureBucketExistsAsync();
            var objectName = $"test-file-{Guid.NewGuid():N}.txt";
            var testContent = "Hello, World! This is a test file for file upload/download.";
            var tempFilePath = Path.GetTempFileName();
            var downloadFilePath = Path.GetTempFileName();

            try
            {
                // Create test file
                await File.WriteAllTextAsync(tempFilePath, testContent);
                _output.WriteLine($"Created temp file: {tempFilePath}");

                // Test upload file
                var uploadResult = await _storageService.UploadFileAsync(
                    _testBucketName,
                    objectName,
                    tempFilePath,
                    "text/plain");

                Assert.NotNull(uploadResult);
                Assert.Equal(_testBucketName, uploadResult.BucketName);
                Assert.Equal(objectName, uploadResult.ObjectName);
                _output.WriteLine($"Uploaded file as object: {objectName}");

                _createdObjects.Add(objectName);

                // Test download file
                await _storageService.DownloadFileAsync(_testBucketName, objectName, downloadFilePath);

                var downloadedContent = await File.ReadAllTextAsync(downloadFilePath);
                Assert.Equal(testContent, downloadedContent);
                _output.WriteLine($"Downloaded file content: {downloadedContent}");
            }
            finally
            {
                // Cleanup temp files
                if (File.Exists(tempFilePath))
                    File.Delete(tempFilePath);
                if (File.Exists(downloadFilePath))
                    File.Delete(downloadFilePath);
            }
        }

        [Fact]
        public async Task ListObjects_ShouldWorkCorrectly()
        {
            // Arrange
            await EnsureBucketExistsAsync();
            var objectPrefix = $"test-list-{Guid.NewGuid():N}";
            var objectNames = new[]
            {
                $"{objectPrefix}/file1.txt",
                $"{objectPrefix}/file2.txt",
                $"{objectPrefix}/subfolder/file3.txt"
            };

            try
            {
                // Upload test objects
                foreach (var objectName in objectNames)
                {
                    var content = $"Content of {objectName}";
                    var bytes = System.Text.Encoding.UTF8.GetBytes(content);

                    using var stream = new MemoryStream(bytes);
                    await _storageService.UploadObjectAsync(objectName, stream, _testBucketName);
                    _createdObjects.Add(objectName);
                }

                _output.WriteLine($"Uploaded {objectNames.Length} test objects");

                // Test list all objects with prefix
                var allObjects = await _storageService.ListObjectsAsync(objectPrefix, true, _testBucketName);
                var allObjectsList = allObjects.ToList();

                Assert.True(allObjectsList.Count >= objectNames.Length);
                foreach (var objectName in objectNames)
                {
                    Assert.Contains(allObjectsList, obj => obj.ObjectName == objectName);
                }
                _output.WriteLine($"Listed {allObjectsList.Count} objects recursively");

                // Test list objects non-recursively
                var topLevelObjects = await _storageService.ListObjectsAsync(objectPrefix, false, _testBucketName);
                var topLevelObjectsList = topLevelObjects.ToList();

                _output.WriteLine($"Listed {topLevelObjectsList.Count} objects non-recursively");
            }
            finally
            {
                // Cleanup will be done in Dispose
            }
        }

        [Fact]
        public async Task DeleteOperations_ShouldWorkCorrectly()
        {
            // Arrange
            await EnsureBucketExistsAsync();
            var objectName = $"test-delete-{Guid.NewGuid():N}.txt";
            var testContent = "This file will be deleted";
            var testBytes = System.Text.Encoding.UTF8.GetBytes(testContent);

            // Upload test object
            using var uploadStream = new MemoryStream(testBytes);
            await _storageService.UploadObjectAsync(objectName, uploadStream, _testBucketName);
            _output.WriteLine($"Uploaded object for deletion test: {objectName}");

            // Verify object exists
            var existsBeforeDelete = await _storageService.ObjectExistsAsync(objectName, _testBucketName);
            Assert.True(existsBeforeDelete);

            // Test delete object
            await _storageService.DeleteObjectAsync(objectName, _testBucketName);
            _output.WriteLine($"Deleted object: {objectName}");

            // Verify object no longer exists
            var existsAfterDelete = await _storageService.ObjectExistsAsync(objectName, _testBucketName);
            Assert.False(existsAfterDelete);
            _output.WriteLine($"Object exists after delete: {existsAfterDelete}");
        }

        [Fact]
        public async Task DeleteMultipleObjects_ShouldWorkCorrectly()
        {
            // Arrange
            await EnsureBucketExistsAsync();
            var objectPrefix = $"test-multi-delete-{Guid.NewGuid():N}";
            var objectNames = new[]
            {
                $"{objectPrefix}/file1.txt",
                $"{objectPrefix}/file2.txt",
                $"{objectPrefix}/file3.txt"
            };

            try
            {
                // Upload test objects
                foreach (var objectName in objectNames)
                {
                    var content = $"Content of {objectName}";
                    var bytes = System.Text.Encoding.UTF8.GetBytes(content);

                    using var stream = new MemoryStream(bytes);
                    await _storageService.UploadObjectAsync(objectName, stream, _testBucketName);
                }

                _output.WriteLine($"Uploaded {objectNames.Length} objects for multi-delete test");

                // Test delete multiple objects
                var deleteResults = await _storageService.DeleteObjectsAsync(objectNames, _testBucketName);
                var deleteResultsList = deleteResults.ToList();

                Assert.Equal(objectNames.Length, deleteResultsList.Count);
                foreach (var result in deleteResultsList)
                {
                    Assert.True(result.Success, $"Failed to delete {result.ObjectName}: {result.ErrorMessage}");
                }

                _output.WriteLine($"Successfully deleted {deleteResultsList.Count} objects");

                // Verify objects no longer exist
                foreach (var objectName in objectNames)
                {
                    var exists = await _storageService.ObjectExistsAsync(_testBucketName, objectName);
                    Assert.False(exists, $"Object {objectName} still exists after deletion");
                }
            }
            finally
            {
                // Objects should be deleted by the test, but just in case
                foreach (var objectName in objectNames)
                {
                    try
                    {
                        await _storageService.DeleteObjectAsync(_testBucketName, objectName);
                    }
                    catch
                    {
                        // Ignore errors during cleanup
                    }
                }
            }
        }

        [Fact]
        public async Task PresignedUrls_ShouldWorkCorrectly()
        {
            // Arrange
            await EnsureBucketExistsAsync();
            var objectName = $"test-presigned-{Guid.NewGuid():N}.txt";
            var testContent = "This is a test file for presigned URLs";
            var testBytes = System.Text.Encoding.UTF8.GetBytes(testContent);

            try
            {
                // Upload test object
                using var uploadStream = new MemoryStream(testBytes);
                await _storageService.UploadObjectAsync(objectName, uploadStream, _testBucketName);
                _createdObjects.Add(objectName);
                _output.WriteLine($"Uploaded object for presigned URL test: {objectName}");

                // Test presigned download URL
                var downloadUrl = await _storageService.GetPresignedDownloadUrlAsync(
                    objectName,
                    TimeSpan.FromHours(1),
                    _testBucketName);

                Assert.NotNull(downloadUrl);
                Assert.StartsWith("http", downloadUrl);
                _output.WriteLine($"Generated presigned download URL: {downloadUrl}");

                // Test presigned upload URL
                var uploadUrl = await _storageService.GetPresignedUploadUrlAsync(
                    $"upload-{objectName}",
                    TimeSpan.FromHours(1),
                    _testBucketName);

                Assert.NotNull(uploadUrl);
                Assert.StartsWith("http", uploadUrl);
                _output.WriteLine($"Generated presigned upload URL: {uploadUrl}");

                // Test preview URL
                var previewUrl = await _storageService.GetPreviewUrlAsync(_testBucketName, objectName);

                Assert.NotNull(previewUrl);
                Assert.StartsWith("http", previewUrl);
                _output.WriteLine($"Generated preview URL: {previewUrl}");
            }
            finally
            {
                // Cleanup will be done in Dispose
            }
        }

        private async Task EnsureBucketExistsAsync()
        {
            var bucketExists = await _storageService.BucketExistsAsync(_testBucketName);
            if (!bucketExists)
            {
                await _storageService.CreateBucketAsync(_testBucketName);
                _output.WriteLine($"Created test bucket: {_testBucketName}");
            }
        }

        public void Dispose()
        {
            try
            {
                // Cleanup created objects
                if (_createdObjects.Any())
                {
                    _output.WriteLine($"Cleaning up {_createdObjects.Count} created objects...");

                    var deleteTask = _storageService.DeleteObjectsAsync(_createdObjects, _testBucketName);
                    deleteTask.Wait(TimeSpan.FromMinutes(2));

                    _output.WriteLine("Cleanup completed");
                }

                // Cleanup test bucket
                var deleteBucketTask = _storageService.DeleteBucketAsync(_testBucketName);
                deleteBucketTask.Wait(TimeSpan.FromMinutes(1));
                _output.WriteLine($"Deleted test bucket: {_testBucketName}");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Error during cleanup: {ex.Message}");
            }
            finally
            {
                _storageService?.Dispose();
                _serviceProvider?.GetService<IServiceScope>()?.Dispose();
            }
        }
    }
}
